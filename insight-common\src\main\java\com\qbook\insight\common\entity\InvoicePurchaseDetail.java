package com.qbook.insight.common.entity;

import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 进项发票-信息汇总
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class InvoicePurchaseDetail extends BaseEntity {
  // 所属任务编号
  private String taskId;

  // 销方税号
  private String taxId;

  // 发票代码
  private String zzfpdm;

  // 发票号码
  private String zzfphm;

  // 数电票号码
  private String fpkjfxlxdm;

  // 购方名称
  private String gmfmc;

  // 购方识别号
  private String gmfnsrsbh;

  // 开票日期
  private Date kprq;

  // 税收分类编码
  private String ssflbm;

  // 特定业务类型
  private String tdywlx;

  // 货物或应税劳务名称
  private String hwmc;

  // 货物名称
  private String easyHwmc;

  // 规格型号
  private String ggxh;

  // 计量单位名称
  private String dw;

  // 计量单位符号
  private String dwSymbol;

  // 数量
  private BigDecimal xssl;

  // 单价
  private BigDecimal dj;

  // 金额
  private BigDecimal hjje;

  // 税率
  private String sl;

  // 税额
  private BigDecimal hjse;

  // 价税合计
  private BigDecimal jshj;

  // 发票来源
  private String fplydm;

  // 发票票种
  private String fppzdm;

  // 发票状态
  private String fpztdm;

  // 发票风险等级
  private String sflzfp;

  // 开票人
  private String kpr;

  // 备注
  private String bz;

  // 开票日期年
  private Integer kprqn;

  // 开票日期月
  private Integer kprqy;

  // 采集时间
  private Date gatherDatetime;

  // 创建时间
  private Date createTime;
}
