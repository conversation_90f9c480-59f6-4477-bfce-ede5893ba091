package com.qbook.insight.analyzer.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qbook.insight.analyzer.util.UniversalResultBuilder;
import com.qbook.insight.common.domain.report.result.Result;
import com.qbook.insight.common.entity.IndicatorConfig;
import com.qbook.insight.common.entity.Report;
import com.qbook.insight.common.mapper.IndicatorConfigMapper;
import com.qbook.insight.common.util.IndicatorCalculator;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
@Slf4j
public class IndicatorCalculatorTest {

  @Resource private IndicatorCalculator indicatorCalculator;
  @Resource private IndicatorConfigMapper indicatorConfigMapper;
  @Resource private AnaResultService anaResultService;

  @Test
  void testAnaReportResult() {

    Report report = new Report();
    report.setUserId(3L);
    report.setId(82L);
    report.setCorpId(1L);
    anaResultService.anaReportResult(report);
  }

  @Test
  void testCalculateInvoicePurchaseAmountYear() {

    LambdaQueryWrapper<IndicatorConfig> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(IndicatorConfig::getStatus, 1);
    List<IndicatorConfig> configList = indicatorConfigMapper.selectList(wrapper);

    String indicatorCode = "taxAnalysis.corporateIncomeTax";
    String indicatorCode1 = "taxAnalysis.corporateIncomeTax.incomeAdjustments.deemedSalesIncome";
    String indicatorCode2 =
        "taxAnalysis.corporateIncomeTax.incomeAdjustments.unrecordedRentInterestIncome";

    Set<String> codeSet = new HashSet<>();
    codeSet.add(indicatorCode);
    codeSet.add(indicatorCode1);
    codeSet.add(indicatorCode2);

    Map<String, Object> map = new HashMap<>();
    map.put("tax_id", "91330681MA2D7C9R1B");
    //    map.put("year", 2023);

    Map<String, Object> resultMap =
        configList.stream()
            .filter(config -> codeSet.contains(config.getIndicatorCode()))
            .map(config -> indicatorCalculator.calculate(config, map))
            .flatMap(m -> m.entrySet().stream())
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    log.info("报告指标结果:{}", resultMap);

    // 构造Result对象
    Result result = UniversalResultBuilder.buildResult(resultMap);
    log.info("报告结果:{}", result);
  }
}
