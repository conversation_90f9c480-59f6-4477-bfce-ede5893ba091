package com.qbook.insight.handler.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qbook.insight.common.entity.Corp;
import com.qbook.insight.common.vo.PageParam;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * 公司操作接口
 *
 * <AUTHOR>
 */
@Service
public interface CorpService {

  /** 添加公司 */
  int add(Corp corp);

  /** 删除公司 */
  int delete(long id);

  /** 修改公司 */
  int update(long id, Corp corp);

  /** 获取公司列表 */
  List<Corp> list(String corpName, String taxId);

  /** 获取公司列表(分页) */
  IPage<Corp> page(PageParam pageParam);
}
