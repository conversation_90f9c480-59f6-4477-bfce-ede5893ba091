package com.qbook.insight.common.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qbook.insight.common.constant.DsName;
import com.qbook.insight.common.entity.InvoicePurchase;
import org.apache.ibatis.annotations.Mapper;

/**
 * 进项Mapper
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-06-04 15:11
 */
@Mapper
@DS(DsName.DATA)
public interface InvoicePurchaseMapper extends BaseMapper<InvoicePurchase> {}
