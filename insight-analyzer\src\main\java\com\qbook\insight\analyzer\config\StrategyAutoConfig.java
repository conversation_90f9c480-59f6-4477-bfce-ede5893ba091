package com.qbook.insight.analyzer.config;

import com.qbook.insight.analyzer.stratetgy.AnalysisStrategy;
import com.qbook.insight.analyzer.stratetgy.AnalysisStrategyManager;
import java.util.Set;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 自动配置-分析策略Bean
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2025-06-03 14:13
 */
@Configuration
public class StrategyAutoConfig {

  /**
   * 创建分析策略管理器
   *
   * @param analyzeStrategies 策略类集合
   * @return new AnalyzeStrategyManager
   */
  @Bean
  @Primary
  public AnalysisStrategyManager analyzeStrategy(Set<AnalysisStrategy> analyzeStrategies) {
    return new AnalysisStrategyManager(analyzeStrategies);
  }
}
