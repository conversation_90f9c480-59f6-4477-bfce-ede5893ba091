package com.qbook.insight.handler.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qbook.insight.common.exception.BizException;
import com.qbook.insight.common.vo.PageParam;
import com.qbook.insight.handler.entity.Member;
import com.qbook.insight.handler.entity.MemberPackage;
import com.qbook.insight.handler.mapper.MemberMapper;
import com.qbook.insight.handler.service.MemberPackageService;
import com.qbook.insight.handler.service.MemberService;
import com.qbook.insight.handler.service.UserService;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 会员信息接口实现
 *
 * <AUTHOR>
 */
@Service
public class MemberServiceImpl implements MemberService {

  @Resource private MemberMapper memberMapper;
  @Resource private UserService userService;
  @Resource private MemberPackageService memberPackageService;

  @Override
  public Member info() {
    LambdaQueryWrapper<Member> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(Member::getUserId, userService.getUserId());
    return memberMapper.selectOne(wrapper);
  }

  @Override
  public int update(long id, Member member) {
    member.setId(id);
    return memberMapper.updateById(member);
  }

  @Override
  public Member getById(long id) {
    return memberMapper.selectById(id);
  }

  @Override
  public List<Member> list(Integer isActive) {
    LambdaQueryWrapper<Member> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(Member::getUserId, userService.getUserId());
    wrapper.eq(isActive != null, Member::getIsActive, isActive);
    wrapper.orderByDesc(Member::getCreatedAt);
    return memberMapper.selectList(wrapper);
  }

  @Override
  public IPage<Member> page(PageParam pageParam, Integer isActive) {
    IPage<Member> page = new Page<>(pageParam.getPage(), pageParam.getPageSize());
    LambdaQueryWrapper<Member> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(Member::getUserId, userService.getUserId());
    wrapper.eq(isActive != null, Member::getIsActive, isActive);
    wrapper.orderByDesc(Member::getCreatedAt);
    return memberMapper.selectPage(page, wrapper);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void handlePaymentSuccess(Long userId, Integer planId) {
    // 获取套餐信息
    MemberPackage pkg = memberPackageService.getById(planId);
    if (pkg == null) {
      throw new BizException("套餐不存在");
    }

    // 查询用户当前的会员信息
    LambdaQueryWrapper<Member> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(Member::getUserId, userId);
    Member existingMember = memberMapper.selectOne(wrapper);

    Date now = new Date();
    Date startDate = now;
    if (existingMember != null && existingMember.getEndDate().after(now)) {
      startDate = existingMember.getStartDate();
    }
    // 计算结束时间
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(now);
    calendar.add(Calendar.MONTH, pkg.getDuration());
    Date endDate = calendar.getTime();

    if (existingMember != null) {
      // 更新现有会员信息
      existingMember.setStartDate(startDate);
      existingMember.setEndDate(endDate);
      existingMember.setFeatures(pkg.getFeatures());
      existingMember.setTotalReports(
          (existingMember.getTotalReports() != null ? existingMember.getTotalReports() : 0)
              + pkg.getReportCount());
      existingMember.setRemainingReports(
          (existingMember.getRemainingReports() != null ? existingMember.getRemainingReports() : 0)
              + pkg.getReportCount());
      existingMember.setIsActive(1);
      existingMember.setUpdatedAt(now);

      memberMapper.updateById(existingMember);
    } else {
      // 新增会员信息
      Member member = new Member();
      member.setUserId(userId);
      member.setStartDate(startDate);
      member.setEndDate(endDate);
      member.setIsActive(1);
      member.setFeatures(pkg.getFeatures());
      member.setTotalReports(pkg.getReportCount());
      member.setRemainingReports(pkg.getReportCount());
      member.setCreatedAt(now);
      member.setUpdatedAt(now);
      memberMapper.insert(member);
    }
  }

  @Override
  public Member getCurrentMember() {
    Date now = new Date();
    LambdaQueryWrapper<Member> wrapper = new LambdaQueryWrapper<>();
    wrapper
        .eq(Member::getUserId, userService.getUserId())
        .eq(Member::getIsActive, 1)
        .le(Member::getStartDate, now)
        .ge(Member::getEndDate, now)
        .orderByDesc(Member::getId)
        .last("LIMIT 1");

    return memberMapper.selectOne(wrapper);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void consumeReport() {
    Member member = getCurrentMember();
    if (member == null) {
      throw new BizException("您还不是会员或会员已过期，请先购买会员套餐");
    }
    if (member.getRemainingReports() <= 0) {
      throw new BizException("您的剩余报告次数不足，请先购买会员套餐");
    }

    LambdaUpdateWrapper<Member> updateWrapper = new LambdaUpdateWrapper<>();
    updateWrapper
        .eq(Member::getId, member.getId())
        .gt(Member::getRemainingReports, 0)
        .setSql("remaining_reports = remaining_reports - 1");

    int updated = memberMapper.update(null, updateWrapper);
    if (updated <= 0) {
      throw new BizException("系统错误，请稍后重试");
    }
  }
}
