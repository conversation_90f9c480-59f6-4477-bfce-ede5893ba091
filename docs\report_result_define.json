{"version": "202501", "basicInfo": {"companyName": "诸暨海鼎金属材料有限公司", "creditCode": "91330681MA2D7C9R1B", "registeredAddress": "浙江省绍兴市诸暨市店口镇斗门村斗门自然村", "registrationDate": "2021-02-03", "status": 0, "registeredCapital": 500000, "paidInCapital": 500000, "industry": "金属及金属矿批发", "businessScope": "一般项目：金属材料销售；机械零件、零部件销售；建筑材料销售；机械电气设备销售；五金产品批发；塑料制品销售；金属切割及焊接设备销售；有色金属压延加工(除依法须经批准的项目外，凭营业执照依法自主开展经营活动)。", "taxAuthority": "诸暨市税务局店口税务分局", "legalPerson": {"name": "王海洋", "age": 35, "idNumber": "330100198500000000", "idType": "居民身份证", "origin": "河南省"}, "registrationType": "私营有限责任公司", "taxCreditRating": "A", "isExportEnterprise": 0, "isHighTechEnterprise": 0, "isSmallMicroEnterprise": 1, "taxpayerType": "一般纳税人", "shareholderAnalysis": {"crossShareholdingCompanies": [{"shareholderName": "王海洋", "shareholdingRatio": 100}], "crossExecutivesCompanies": [], "crossLegalRepresentativeCompanies": []}}, "invoiceAnalysis": {"purchaseInvoice": {"riskCompanies": [], "abnormalAccounts": []}, "salesInvoice": {"riskCompanies": [], "abnormalAccounts": []}, "largeConsultingInvoices": [], "retailSupermarketInvoices": []}, "productAnalysis": {"mainPurchaseProducts": [{"name": "黑色金属冶炼压延品+开平板", "unit": "吨", "quantity": 1983, "amount": 7318341, "avgPrice": 3690.54, "percentage": 25.58}, {"name": "热镀锌卷板", "unit": "吨", "quantity": 1250, "amount": 5125000, "avgPrice": 4100.0, "percentage": 17.89}, {"name": "不锈钢冷轧板", "unit": "吨", "quantity": 850, "amount": 4675000, "avgPrice": 5500.0, "percentage": 15.62}, {"name": "铝型材", "unit": "吨", "quantity": 720, "amount": 1440000, "avgPrice": 2000.0, "percentage": 9.85}, {"name": "铜管", "unit": "吨", "quantity": 300, "amount": 1800000, "avgPrice": 6000.0, "percentage": 7.86}], "mainSalesProducts": [{"name": "黑色金属冶炼压延品+开平板", "unit": "吨", "quantity": 1983, "amount": 7318341, "avgPrice": 3690.54, "percentage": 25.58}, {"name": "热镀锌卷板（加工）", "unit": "吨", "quantity": 1200, "amount": 5400000, "avgPrice": 4500.0, "percentage": 18.89}, {"name": "不锈钢制品", "unit": "吨", "quantity": 800, "amount": 4800000, "avgPrice": 6000.0, "percentage": 16.79}, {"name": "铝合金门窗型材", "unit": "吨", "quantity": 650, "amount": 1625000, "avgPrice": 2500.0, "percentage": 8.52}, {"name": "铜制五金件", "unit": "吨", "quantity": 280, "amount": 1960000, "avgPrice": 7000.0, "percentage": 6.86}], "conclusion": "(人工填写)"}, "taxAnalysis": {"corporateIncomeTax": [{"year": 2023, "incomeTaxBurden": 0.06, "industryIncomeTaxBurden": 0.05, "incomeAdjustments": {"deemedSalesIncome": 0, "unrecordedScrapIncome": 1500, "unrecordedRentInterestIncome": 2000, "unrecordedAdvanceReceipts": 3000, "deemedInterestIncome": 1000}, "incomeDeductions": {"nonTaxableGovIncome": 0, "taxExemptBondInterest": 0, "taxExemptDividends": 0, "resourceUtilizationDeduction": 0, "priorPeriodReturns": 0, "priorPeriodOverstatedIncome": 0}, "expenseAdjustments": {"deemedSalesCost": 0, "nonDeductibleSalaries": 5000, "excessWelfare": 2000, "excessUnionFees": 1000, "excessTrainingFees": 0, "excessEntertainment": 3000, "excessAdvertising": 0, "excessDonations": 0, "nonCharitableDonations": 0, "nonDeductibleInterest": 0, "nonDeductiblePenalties": 500, "excessCommission": 0, "nonBusinessSponsorship": 0, "unapprovedReserves": 0, "disabledStaffDeduction": 0, "ecoEquipmentDeduction": 0, "ventureInvestmentDeduction": 0, "nonTaxableExpenses": 0, "irrelevantExpenses": 0, "uninvoicedCosts": 0, "overstatedCosts": 0, "unrecordedCosts": 0, "negativeInventory": 0, "inventoryValuationAdjustment": 0, "negativeMaterials": 0, "productionCosts": 0, "mismatchedInventoryCosts": 0, "personalSocialInsurance": 0, "industryMaterialAnalysis": [], "conclusion": "2023年度企业所得税调整主要集中在工资福利和业务招待费超标"}, "expenseDeductions": {"rdDeduction": 0, "crossPeriodCosts": 0, "smallScaleInvoices": [], "smallScaleInvoiceAdjustment": 0}, "assetAdjustments": {"depreciationAdjustment": 0, "nonDeductibleReserves": 0, "assetLossAdjustment": 0}, "specialAdjustments": {"reorganizationDeferral": 0, "relocationDeferral": 0}}, {"year": 2024, "incomeTaxBurden": 0.07, "industryIncomeTaxBurden": 0.06, "incomeAdjustments": {"deemedSalesIncome": 1, "unrecordedScrapIncome": 2, "unrecordedRentInterestIncome": 3, "unrecordedAdvanceReceipts": 4, "deemedInterestIncome": 5}, "incomeDeductions": {"nonTaxableGovIncome": 0, "taxExemptBondInterest": 0, "taxExemptDividends": 0, "resourceUtilizationDeduction": 0, "priorPeriodReturns": 0, "priorPeriodOverstatedIncome": 0}, "expenseAdjustments": {"deemedSalesCost": 0, "nonDeductibleSalaries": 0, "excessWelfare": 0, "excessUnionFees": 0, "excessTrainingFees": 0, "excessEntertainment": 0, "excessAdvertising": 0, "excessDonations": 0, "nonCharitableDonations": 0, "nonDeductibleInterest": 0, "nonDeductiblePenalties": 0, "excessCommission": 0, "nonBusinessSponsorship": 0, "unapprovedReserves": 0, "disabledStaffDeduction": 0, "ecoEquipmentDeduction": 0, "ventureInvestmentDeduction": 0, "nonTaxableExpenses": 0, "irrelevantExpenses": 0, "uninvoicedCosts": 0, "overstatedCosts": 0, "unrecordedCosts": 0, "negativeInventory": 0, "inventoryValuationAdjustment": 0, "negativeMaterials": 0, "productionCosts": 0, "mismatchedInventoryCosts": 0, "personalSocialInsurance": 0, "industryMaterialAnalysis": [], "conclusion": "（人工辅助）"}, "expenseDeductions": {"rdDeduction": 0, "crossPeriodCosts": 0, "smallScaleInvoices": [], "smallScaleInvoiceAdjustment": 0}, "assetAdjustments": {"depreciationAdjustment": 0, "nonDeductibleReserves": 0, "assetLossAdjustment": 0}, "specialAdjustments": {"reorganizationDeferral": 0, "relocationDeferral": 0}}, {"year": 2025, "incomeTaxBurden": 0.08, "industryIncomeTaxBurden": 0.07, "incomeAdjustments": {"deemedSalesIncome": 2000, "unrecordedScrapIncome": 2500, "unrecordedRentInterestIncome": 1800, "unrecordedAdvanceReceipts": 3200, "deemedInterestIncome": 1500}, "incomeDeductions": {"nonTaxableGovIncome": 0, "taxExemptBondInterest": 0, "taxExemptDividends": 0, "resourceUtilizationDeduction": 0, "priorPeriodReturns": 0, "priorPeriodOverstatedIncome": 0}, "expenseAdjustments": {"deemedSalesCost": 0, "nonDeductibleSalaries": 4500, "excessWelfare": 1800, "excessUnionFees": 1200, "excessTrainingFees": 0, "excessEntertainment": 2800, "excessAdvertising": 0, "excessDonations": 0, "nonCharitableDonations": 0, "nonDeductibleInterest": 0, "nonDeductiblePenalties": 600, "excessCommission": 0, "nonBusinessSponsorship": 0, "unapprovedReserves": 0, "disabledStaffDeduction": 0, "ecoEquipmentDeduction": 0, "ventureInvestmentDeduction": 0, "nonTaxableExpenses": 0, "irrelevantExpenses": 0, "uninvoicedCosts": 0, "overstatedCosts": 0, "unrecordedCosts": 0, "negativeInventory": 0, "inventoryValuationAdjustment": 0, "negativeMaterials": 0, "productionCosts": 0, "mismatchedInventoryCosts": 0, "personalSocialInsurance": 0, "industryMaterialAnalysis": [], "conclusion": "2025年度企业所得税调整与2023年类似，但金额有所增加"}, "expenseDeductions": {"rdDeduction": 0, "crossPeriodCosts": 0, "smallScaleInvoices": [], "smallScaleInvoiceAdjustment": 0}, "assetAdjustments": {"depreciationAdjustment": 0, "nonDeductibleReserves": 0, "assetLossAdjustment": 0}, "specialAdjustments": {"reorganizationDeferral": 0, "relocationDeferral": 0}}], "vat": [{"year": 2023, "vatBurden": 0.82, "addTaxBurden": 0, "industryVatTaxBurden": 0.81, "outputTax": {"unrecordedAdvanceReceipts": 1200, "unrecordedScrapIncome": 800, "unrecordedDeemedSales": 0, "unrecordedSurcharges": 0, "incorrectDiscounts": 0, "incorrectTaxRate": 0, "deemedInterestSales": 0, "creditNoteList": []}, "inputTax": {"nonCompliantInvoices": 3500, "nonCompliantInvoiceList": {"fraudulentInvoices": [], "overduePayables": [], "mismatchedItems": [], "unusedInventory": [], "mismatchedCostInventory": []}, "nonDeductibleInputs": 2800, "nonDeductibleList": [], "abnormalLoss": 0, "specialItemDeductions": 0}, "otherVat": {"understatedHighRateItems": 0, "incorrectSimplifiedMethod": 0, "lowTaxBurden": 0, "energyBasedEstimate": 0, "zeroRateInvoices": [], "taxExemptInvoices": []}}, {"year": 2024, "vatBurden": 0.83, "addTaxBurden": 0, "industryVatTaxBurden": 0.82, "outputTax": {"unrecordedAdvanceReceipts": 0, "unrecordedScrapIncome": 0, "unrecordedDeemedSales": 0, "unrecordedSurcharges": 0, "incorrectDiscounts": 0, "incorrectTaxRate": 0, "deemedInterestSales": 0, "creditNoteList": []}, "inputTax": {"nonCompliantInvoices": 0, "nonCompliantInvoiceList": {"fraudulentInvoices": [], "overduePayables": [], "mismatchedItems": [], "unusedInventory": [], "mismatchedCostInventory": []}, "nonDeductibleInputs": 0, "nonDeductibleList": [], "abnormalLoss": 0, "specialItemDeductions": 0}, "otherVat": {"understatedHighRateItems": 0, "incorrectSimplifiedMethod": 0, "lowTaxBurden": 0, "energyBasedEstimate": 0, "zeroRateInvoices": [], "taxExemptInvoices": []}}, {"year": 2025, "vatBurden": 0.84, "addTaxBurden": 0, "industryVatTaxBurden": 0.83, "outputTax": {"unrecordedAdvanceReceipts": 1500, "unrecordedScrapIncome": 1000, "unrecordedDeemedSales": 0, "unrecordedSurcharges": 0, "incorrectDiscounts": 0, "incorrectTaxRate": 0, "deemedInterestSales": 0, "creditNoteList": []}, "inputTax": {"nonCompliantInvoices": 4200, "nonCompliantInvoiceList": {"fraudulentInvoices": [], "overduePayables": [], "mismatchedItems": [], "unusedInventory": [], "mismatchedCostInventory": []}, "nonDeductibleInputs": 3200, "nonDeductibleList": [], "abnormalLoss": 0, "specialItemDeductions": 0}, "otherVat": {"understatedHighRateItems": 0, "incorrectSimplifiedMethod": 0, "lowTaxBurden": 0, "energyBasedEstimate": 0, "zeroRateInvoices": [], "taxExemptInvoices": []}}], "propertyTax": [{"year": 2023, "landValueOmission": 0, "calculationError": 0, "unpaidRelatedPartyRent": 0}, {"year": 2024, "landValueOmission": 0, "calculationError": 0, "unpaidRelatedPartyRent": 0}, {"year": 2025, "landValueOmission": 0, "calculationError": 0, "unpaidRelatedPartyRent": 0}], "landUseTax": [{"year": 2023, "incorrectLandGradeRate": 0, "incorrectLandArea": 0}, {"year": 2024, "incorrectLandGradeRate": 0, "incorrectLandArea": 0}, {"year": 2025, "incorrectLandGradeRate": 0, "incorrectLandArea": 0}], "consumptionTax": [{"year": 2023, "incorrectTaxRate": 0, "unrecordedGifts": 0}, {"year": 2024, "incorrectTaxRate": 0, "unrecordedGifts": 0}, {"year": 2025, "incorrectTaxRate": 0, "unrecordedGifts": 0}], "stampDuty": [{"year": 2023, "incorrectTaxCategory": 0, "unrecordedCapitalIncrease": 0, "unrecordedEquityChanges": 0, "unrecordedHiddenIncome": 0}, {"year": 2024, "incorrectTaxCategory": 0, "unrecordedCapitalIncrease": 0, "unrecordedEquityChanges": 0, "unrecordedHiddenIncome": 0}, {"year": 2025, "incorrectTaxCategory": 0, "unrecordedCapitalIncrease": 0, "unrecordedEquityChanges": 0, "unrecordedHiddenIncome": 0}], "urbanMaintenanceTax": [{"year": 2023, "unrecordedExportCredit": 0, "estimatedUnderpayment": 0, "unrecordedHiddenIncome": 0}, {"year": 2024, "unrecordedExportCredit": 0, "estimatedUnderpayment": 0, "unrecordedHiddenIncome": 0}, {"year": 2025, "unrecordedExportCredit": 0, "estimatedUnderpayment": 0, "unrecordedHiddenIncome": 0}], "educationSurcharge": [{"year": 2023, "unrecordedExportCredit": 0, "estimatedUnderpayment": 0, "unrecordedHiddenIncome": 0}, {"year": 2024, "unrecordedExportCredit": 0, "estimatedUnderpayment": 0, "unrecordedHiddenIncome": 0}, {"year": 2025, "unrecordedExportCredit": 0, "estimatedUnderpayment": 0, "unrecordedHiddenIncome": 0}], "landAppreciationTax": [{}], "resourceTax": [{}], "individualIncomeTax": [{"year": 2023, "unwithheldLaborIncome": 0, "deemedDividends": 0, "deemedGiftSales": 0, "splitExecutiveSalaries": 0, "specialNotes": 0}, {"year": 2024, "unwithheldLaborIncome": 0, "deemedDividends": 0, "deemedGiftSales": 0, "splitExecutiveSalaries": 0, "specialNotes": 0}, {"year": 2025, "unwithheldLaborIncome": 0, "deemedDividends": 0, "deemedGiftSales": 0, "splitExecutiveSalaries": 0, "specialNotes": 0}], "socialInsurance": [{"year": 2023, "insufficientSocialInsurance": 0}, {"year": 2024, "insufficientSocialInsurance": 0}, {"year": 2025, "insufficientSocialInsurance": 0}]}, "financialAnalysis": {"receivablesAnalysis": null, "inventoryAnalysis": null, "managementExpenses": null, "salesExpenses": null, "financialExpenses": null, "efficiencyAnalysis": null}, "policyBenefits": {"industrySubsidy": "", "talentSubsidy": "", "rentSubsidy": "", "equipmentSubsidy": "", "ipoSubsidy": "", "otherSubsidies": ""}, "planningAnalysis": {"corporateStructure": "(Manual Input)", "investmentAnalysis": "(Manual Input)"}}