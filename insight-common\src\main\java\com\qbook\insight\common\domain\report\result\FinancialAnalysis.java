package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 财务分析
 *
 * <AUTHOR>
 */
@Data
public class FinancialAnalysis {

  @ApiModelProperty("应收账款分析")
  private Object receivablesAnalysis;

  @ApiModelProperty("存货分析")
  private Object inventoryAnalysis;

  @ApiModelProperty("管理费用")
  private Object managementExpenses;

  @ApiModelProperty("销售费用")
  private Object salesExpenses;

  @ApiModelProperty("财务费用")
  private Object financialExpenses;

  @ApiModelProperty("工效分析")
  private Object efficiencyAnalysis;
}
