package com.qbook.insight.collector.service;

import com.qbook.insight.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Service;

/**
 * MQ 队列监听器
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MqReceiver {

  @RabbitListener(queues = "fkservice_result_queue")
  public void listen(String result) {
    if (!StringUtils.hasLength(result)) {
      return;
    }

    log.info("{} {}", "[RESULT]", result);
  }
}
