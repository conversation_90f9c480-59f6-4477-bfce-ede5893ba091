package com.qbook.insight.common.domain.report.result;

import com.qbook.insight.common.annotation.ResultLevel1;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 个人所得税
 *
 * <AUTHOR>
 */
@Data
public class IndividualIncomeTax {

  @ApiModelProperty("序号")
  @ResultLevel1
  private Integer index;

  @ApiModelProperty("年份")
  @ResultLevel1
  private Integer year;

  @ApiModelProperty("个人代开的劳务报酬未代扣代缴税款(元)")
  @ResultLevel1
  private BigDecimal unwithheldLaborIncome;

  @ApiModelProperty("股东其他应收款挂账被视同分红未代扣代缴(元)")
  @ResultLevel1
  private BigDecimal deemedDividends;

  @ApiModelProperty("对外赠送被视同销售未代扣代缴(元)")
  @ResultLevel1
  private BigDecimal deemedGiftSales;

  @ApiModelProperty("高管薪资收入分拆应代扣代缴税款(元)")
  private BigDecimal splitExecutiveSalaries;

  @ApiModelProperty("特别提醒")
  private BigDecimal specialNotes;
}
