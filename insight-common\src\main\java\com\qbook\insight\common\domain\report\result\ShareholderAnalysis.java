package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * 股东及关联方分析
 *
 * <AUTHOR>
 */
@Data
public class ShareholderAnalysis {

  @ApiModelProperty("股东交叉的企业列表")
  private List<Object> crossShareholdingCompanies;

  @ApiModelProperty("高管交叉的企业列表")
  private List<Object> crossExecutivesCompanies;

  @ApiModelProperty("法定代表人交叉的企业列表")
  private List<Object> crossLegalRepresentativeCompanies;
}
