package com.qbook.insight.common.domain.report.result;

import com.qbook.insight.common.annotation.ResultLevel1;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 法人信息
 *
 * <AUTHOR>
 */
@Data
public class LegalPerson {

  @ApiModelProperty("法人姓名")
  @ResultLevel1
  private String name;

  @ApiModelProperty("法人年龄")
  @ResultLevel1
  private Integer age;

  @ApiModelProperty("法人证件")
  @ResultLevel1
  private String idNumber;

  @ApiModelProperty("法人证件类型")
  @ResultLevel1
  private String idType;

  @ApiModelProperty("法人籍贯")
  @ResultLevel1
  private String origin;
}
