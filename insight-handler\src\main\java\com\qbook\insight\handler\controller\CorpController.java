package com.qbook.insight.handler.controller;

import com.qbook.insight.common.domain.R;
import com.qbook.insight.common.entity.Corp;
import com.qbook.insight.common.vo.PageParam;
import com.qbook.insight.handler.service.CorpService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * 公司相关接口
 *
 * <AUTHOR>
 */
@Api(tags = "Corporation Operate", description = "公司操作")
@RestController
@RequestMapping("/corp")
public class CorpController {

  @Resource private CorpService corpService;

  @ApiOperation(value = "添加公司")
  @PostMapping("/add")
  public R add(@RequestBody Corp corp) {
    return R.ok(corpService.add(corp));
  }

  @ApiOperation(value = "删除公司")
  @DeleteMapping("/{id}")
  public R delete(@PathVariable long id) {
    return R.ok(corpService.delete(id));
  }

  @ApiOperation(value = "修改公司")
  @PutMapping("/{id}")
  public R update(@PathVariable long id, @RequestBody Corp corp) {
    return R.ok(corpService.update(id, corp));
  }

  @ApiOperation(value = "获取公司列表")
  @GetMapping("/list")
  public R list(
      @ApiParam("公司名称") @RequestParam(required = false) String corpName,
      @ApiParam("社会信用代码(税号)") @RequestParam(required = false) String taxId) {
    return R.ok(corpService.list(corpName, taxId));
  }

  @ApiOperation(value = "获取公司列表(分页)")
  @GetMapping("/page")
  public R page(
      PageParam pageParam, @ApiParam("查询字段") @RequestParam(required = false) String searchField) {
    return R.ok(corpService.page(pageParam));
  }
}
