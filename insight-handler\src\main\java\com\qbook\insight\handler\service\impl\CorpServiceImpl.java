package com.qbook.insight.handler.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qbook.insight.common.entity.Corp;
import com.qbook.insight.common.mapper.CorpMapper;
import com.qbook.insight.common.util.StringUtils;
import com.qbook.insight.common.vo.PageParam;
import com.qbook.insight.handler.service.CorpService;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 公司操作接口实现
 *
 * <AUTHOR>
 */
@Service
public class CorpServiceImpl implements CorpService {

  @Resource private CorpMapper corpMapper;

  @Override
  public int add(Corp corp) {
    return corpMapper.insert(corp);
  }

  @Override
  public int delete(long id) {
    return corpMapper.deleteById(id);
  }

  @Override
  public int update(long id, Corp corp) {
    corp.setId(id);
    return corpMapper.updateById(corp);
  }

  @Override
  public List<Corp> list(String corpName, String taxId) {
    LambdaQueryWrapper<Corp> wrapper = new LambdaQueryWrapper<>();
    wrapper.like(StringUtils.isNotBlank(corpName), Corp::getName, corpName);
    wrapper.eq(StringUtils.isNotBlank(taxId), Corp::getTaxId, taxId);
    return corpMapper.selectList(wrapper);
  }

  @Override
  public IPage<Corp> page(PageParam pageParam) {
    IPage<Corp> page = new Page<>(pageParam.getPage(), pageParam.getPageSize());
    return corpMapper.selectPage(page, null);
  }
}
