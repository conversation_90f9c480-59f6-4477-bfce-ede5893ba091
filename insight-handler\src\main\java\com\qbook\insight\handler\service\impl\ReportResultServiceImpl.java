package com.qbook.insight.handler.service.impl;

import cn.hutool.json.JSONUtil;
import com.qbook.insight.common.annotation.ResultLevel1;
import com.qbook.insight.common.constant.RMsg;
import com.qbook.insight.common.constant.Role;
import com.qbook.insight.common.domain.report.result.Result;
import com.qbook.insight.common.exception.BizException;
import com.qbook.insight.common.mapper.ReportResultMapper;
import com.qbook.insight.common.util.JsonUpdateMapperHelper;
import com.qbook.insight.common.util.ReportResultFilter;
import com.qbook.insight.handler.service.ReportResultService;
import com.qbook.insight.handler.service.UserService;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 报告结果操作
 *
 * <AUTHOR>
 */
@Service
public class ReportResultServiceImpl implements ReportResultService {

  @Resource private ReportResultMapper reportResultMapper;
  @Resource private UserService userService;

  @Transactional(rollbackFor = Exception.class)
  public int update(Long reportId, Result result) {
    if (reportId == null || result == null) {
      throw new BizException(RMsg.ERR_PARAM);
    }

    String resultStr = reportResultMapper.selectByReportId(reportId, null);
    if (resultStr == null) {
      throw new BizException("该报告结果不存在");
    }

    Map<String, Object> params = JsonUpdateMapperHelper.buildJsonUpdate(result, "result");
    params.put("report_id", reportId);
    return reportResultMapper.updateJsonColumn(params);
  }

  public String getReportResult(Long reportId, int level) {
    if (reportId == null) {
      throw new BizException(RMsg.ERR_PARAM);
    }
    if (level != 1 && level != 2) {
      throw new BizException(RMsg.ERR_PARAM);
    }

    // 普通用户只能看自己的报告
    Long userId = null;
    if (!userService.hasAnyRoles(Role.ADMIN, Role.AUDITOR)) {
      userId = userService.getUserId();
    }

    String resultStr = reportResultMapper.selectByReportId(reportId, userId);
    if (resultStr == null) {
      throw new BizException("该报告结果不存在或无权查看");
    }

    if (level == 1) {
      Result result = JSONUtil.toBean(resultStr, Result.class);
      Map<String, Object> level1Map = ReportResultFilter.process(result, ResultLevel1.class);
      return JSONUtil.toJsonStr(level1Map);
    } else {
      return resultStr;
    }
  }
}
