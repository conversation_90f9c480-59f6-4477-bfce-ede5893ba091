package com.qbook.insight.handler.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qbook.insight.common.util.HttpClient;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2025-06-16 下午4:05
 * @version V1.0
 */
@Slf4j
class WeChatServiceTest {

  // 测试号的appid和appsecret
  private static final String APPID = "wx79e346bc971a4bc4";
  private static final String APPSECRET = "7ceb41bc895d9aab0467bc6fdc9c903d";

  @Test
  void getAccessToken() {
    String url =
        "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid="
            + APPID
            + "&secret="
            + APPSECRET;
    try {
      HttpClient.Response response = HttpClient.get(url, null, 5000);
      System.out.println("getAccessToken:" + response.getData());
      System.out.println("getAccessToken:" + parseResponse(response.getData(), "/access_token"));

    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  @Test
  void createQrCode() {}

  @Test
  void getQrCode() {}

  @Test
  void parseResponse() {}

  public String parseResponse(String jsonStr, String key) {
    ObjectMapper objectMapper = new ObjectMapper();
    try {
      JsonNode rootNode = objectMapper.readTree(jsonStr);
      return rootNode.at(key).asText();
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }
}
