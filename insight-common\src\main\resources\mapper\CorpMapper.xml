<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qbook.insight.common.mapper.CorpMapper">
  <select id="selectCorpInfoById" resultType="java.util.Map">
    SELECT
    c.name AS companyName,
    c.tax_id AS creditCode,
    c.address AS registeredAddress,
    c.eastab_date AS registrationDate,
    c.state AS status,
    c.register_capital AS registeredCapital,
    c.paid_in_capital AS paidInCapital,
    c.sector AS industry,
    cd.op_scope AS businessScope,
    cd.reg_org_name AS taxAuthority,
    cd.frname AS name,
    CASE
    WHEN LENGTH(cd.fr_id_card) = 18 THEN
    YEAR(CURDATE()) - YEAR(STR_TO_DATE(SUBSTRING(cd.fr_id_card, 7, 8), '%Y%m%d'))
    - (CASE
    WHEN DATE_FORMAT(CURDATE(), '%m%d') &lt;
    DATE_FORMAT(STR_TO_DATE(SUBSTRING(cd.fr_id_card, 7, 8), '%Y%m%d'),
    '%m%d')
    THEN 1 ELSE 0
    END)
    WHEN LENGTH(cd.fr_id_card) = 15 THEN
    YEAR(CURDATE()) - (1900 + CAST(SUBSTRING(cd.fr_id_card, 7, 2) AS UNSIGNED))
    - (CASE
    WHEN DATE_FORMAT(CURDATE(), '%m%d') &lt;
    DATE_FORMAT(STR_TO_DATE(CONCAT('19', SUBSTRING(cd.fr_id_card, 7, 6)),
    '%Y%m%d'), '%m%d')
    THEN 1 ELSE 0
    END)
    ELSE NULL
    END AS age,
    cd.fr_id_card AS idNumber,
    cd.fr_id_type AS idType,
    cd.fr_origin AS origin,
    cd.cap_type_name AS registrationType,
    cd.credit_rating AS taxCreditRating,
    cd.is_export_company AS isExportEnterprise,
    cd.is_high_tech AS isHighTechEnterprise,
    cd.is_small_micro AS isSmallMicroEnterprise,
    cd.taxpayer_type AS taxpayerType
    FROM
    corp c
    LEFT JOIN corp_detail cd ON c.id = cd.corp_id
    WHERE
    c.id = #{id}
  </select>
</mapper>
