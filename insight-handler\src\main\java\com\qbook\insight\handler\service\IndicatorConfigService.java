package com.qbook.insight.handler.service;

import com.qbook.insight.common.entity.IndicatorConfig;
import java.util.List;

/**
 * 指标计算配置接口
 *
 * <AUTHOR>
 */
public interface IndicatorConfigService {

  /** 添加数据 */
  int add(IndicatorConfig config);

  /** 删除数据 */
  int delete(long id);

  /** 修改数据 */
  int update(long id, IndicatorConfig config);

  /** 获取列表 */
  List<IndicatorConfig> list(Integer status, String indicatorName, String indicatorCode);
}
