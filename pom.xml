<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
     xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.qbook.insight</groupId>
  <artifactId>insight-parent</artifactId>
  <version>${revision}</version>

  <packaging>pom</packaging>

  <modules>
    <module>insight-handler</module>
    <module>insight-collector</module>
    <module>insight-analyzer</module>
    <module>insight-generator</module>
    <module>insight-common</module>
  </modules>

  <properties>
    <revision>1.0.0-SNAPSHOT</revision>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <java.version>1.8</java.version>
    <spring-boot.version>2.7.18</spring-boot.version>
    <maven-compiler-plugin.version>3.10.1</maven-compiler-plugin.version>
    <spotless-maven-plugin.version>2.30.0</spotless-maven-plugin.version>
    <druid.version>1.2.23</druid.version>
    <mysql.version>8.3.0</mysql.version>
    <lombok.version>1.18.30</lombok.version>
    <mybatis.plus.version>3.5.12</mybatis.plus.version>
    <dynamic.datasource.version>4.3.1</dynamic.datasource.version>
    <spring.version>5.3.31</spring.version>
    <jackson.version>2.15.3</jackson.version>
    <httpclient.version>4.5.14</httpclient.version>
    <poi.version>3.10.1</poi.version>
    <poixwpf.version>1.0.6</poixwpf.version>
    <docx4j.version>8.3.14</docx4j.version>
    <jfreechart.version>1.5.4</jfreechart.version>
    <fop.version>2.7</fop.version>
    <swagger.version>2.6.1</swagger.version>
    <velocity.version>2.3</velocity.version>
    <commons-lang3.version>3.17.0</commons-lang3.version>
    <swagger-annotations.version>1.5.10</swagger-annotations.version>
    <jjwt.version>0.12.6</jjwt.version>
    <jakarta.annotation.version>1.3.5</jakarta.annotation.version>
    <spring-security.version>5.7.11</spring-security.version>
    <aspectj.version>1.9.7</aspectj.version>
    <hutool.version>5.3.10</hutool.version>
    <xstream.version>1.4.17</xstream.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>cn.hutool</groupId>
        <artifactId>hutool-all</artifactId>
        <version>${hutool.version}</version>
      </dependency>

      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid-spring-boot-starter</artifactId>
        <version>${druid.version}</version>
      </dependency>

      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>dynamic-datasource-spring</artifactId>
        <version>${dynamic.datasource.version}</version>
      </dependency>

      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        <version>${dynamic.datasource.version}</version>
      </dependency>

      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>${mybatis.plus.version}</version>
      </dependency>

      <!-- for pager -->
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-jsqlparser-4.9</artifactId>
        <version>${mybatis.plus.version}</version>
      </dependency>

      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-spring</artifactId>
        <version>${mybatis.plus.version}</version>
      </dependency>

      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-core</artifactId>
        <version>${jackson.version}</version>
      </dependency>

      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>${jackson.version}</version>
      </dependency>

      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-xml</artifactId>
        <version>${jackson.version}</version>
      </dependency>

      <dependency>
        <groupId>com.mysql</groupId>
        <artifactId>mysql-connector-j</artifactId>
        <version>${mysql.version}</version>
        <scope>runtime</scope>
      </dependency>

      <dependency>
        <groupId>com.qbook.insight</groupId>
        <artifactId>insight-common</artifactId>
        <version>${revision}</version>
      </dependency>

      <dependency>
        <groupId>com.thoughtworks.xstream</groupId>
        <artifactId>xstream</artifactId>
        <version>${xstream.version}</version>
      </dependency>

      <dependency>
        <groupId>fr.opensagres.xdocreport</groupId>
        <artifactId>org.apache.poi.xwpf.converter.core</artifactId>
        <version>${poixwpf.version}</version>
      </dependency>

      <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-api</artifactId>
        <version>${jjwt.version}</version>
      </dependency>

      <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-impl</artifactId>
        <version>${jjwt.version}</version>
        <scope>runtime</scope>
      </dependency>

      <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-jackson</artifactId>
        <!-- or jjwt-gson if Gson is preferred -->
        <version>${jjwt.version}</version>
        <scope>runtime</scope>
      </dependency>

      <dependency>
        <groupId>io.springfox</groupId>
        <artifactId>springfox-swagger-ui</artifactId>
        <version>${swagger.version}</version>
      </dependency>

      <dependency>
        <groupId>io.springfox</groupId>
        <artifactId>springfox-swagger2</artifactId>
        <version>${swagger.version}</version>
      </dependency>

      <dependency>
        <groupId>io.swagger</groupId>
        <artifactId>swagger-annotations</artifactId>
        <version>${swagger-annotations.version}</version>
      </dependency>

      <dependency>
        <groupId>jakarta.annotation</groupId>
        <artifactId>jakarta.annotation-api</artifactId>
        <version>${jakarta.annotation.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>${commons-lang3.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>${httpclient.version}</version>
        <exclusions>
          <exclusion>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi</artifactId>
        <version>${poi.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi-ooxml</artifactId>
        <version>${poi.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.velocity</groupId>
        <artifactId>velocity-engine-core</artifactId>
        <version>${velocity.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.xmlgraphics</groupId>
        <artifactId>fop</artifactId>
        <version>${fop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.aspectj</groupId>
        <artifactId>aspectjweaver</artifactId>
        <version>${aspectj.version}</version>
      </dependency>

      <dependency>
        <groupId>org.docx4j</groupId>
        <artifactId>docx4j-core</artifactId>
        <version>${docx4j.version}</version>
      </dependency>

      <dependency>
        <groupId>org.docx4j</groupId>
        <artifactId>docx4j-export-fo</artifactId>
        <version>${docx4j.version}</version>
      </dependency>

      <dependency>
        <groupId>org.docx4j</groupId>
        <artifactId>docx4j-JAXB-Internal</artifactId>
        <version>${docx4j.version}</version>
      </dependency>

      <dependency>
        <groupId>org.docx4j</groupId>
        <artifactId>docx4j-JAXB-ReferenceImpl</artifactId>
        <version>${docx4j.version}</version>
      </dependency>

      <dependency>
        <groupId>org.jfree</groupId>
        <artifactId>jfreechart</artifactId>
        <version>${jfreechart.version}</version>
      </dependency>

      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${lombok.version}</version>
        <optional>true</optional>
      </dependency>

      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-web</artifactId>
        <version>${spring.version}</version>
      </dependency>

      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring-boot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter</artifactId>
        <version>${spring-boot.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
        <version>${spring-boot.version}</version>
        <exclusions>
          <exclusion>
            <groupId>io.lettuce</groupId>
            <artifactId>lettuce-core</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-security</artifactId>
        <version>${spring-boot.version}</version>
      </dependency>

      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
        <version>${spring-boot.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-config</artifactId>
        <version>${spring-security.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-maven-plugin</artifactId>
          <version>${spring-boot.version}</version>
          <configuration>
            <excludes>
              <exclude>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
              </exclude>
            </excludes>
          </configuration>
          <executions>
            <execution>
              <goals>
                <goal>repackage</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${maven-compiler-plugin.version}</version>
        <configuration>
          <source>${java.version}</source>
          <target>${java.version}</target>
          <encoding>${project.build.sourceEncoding}</encoding>

          <annotationProcessorPaths>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>${lombok.version}</version>
            </path>
          </annotationProcessorPaths>
        </configuration>
      </plugin>

      <plugin>
        <groupId>com.diffplug.spotless</groupId>
        <artifactId>spotless-maven-plugin</artifactId>
        <version>${spotless-maven-plugin.version}</version>

        <configuration>
          <formats>
            <format>
              <includes>
                <include>**/resources/**/*.xml</include>
              </includes>

              <eclipseWtp>
                <type>XML</type>
                <version>4.18.0</version>
              </eclipseWtp>

              <endWithNewline/>
              <indent>
                <spaces>true</spaces>
                <spacesPerTab>2</spacesPerTab>
              </indent>
            </format>
          </formats>

          <java>
            <includes>
              <include>**/*.java</include>
            </includes>
            <googleJavaFormat>
              <version>1.7</version>
              <style>GOOGLE</style>
            </googleJavaFormat>
          </java>

          <pom>
            <includes>
              <include>**/pom.xml</include>
            </includes>
            <sortPom>
              <encoding>UTF-8</encoding>
              <indentSchemaLocation>true</indentSchemaLocation>
              <expandEmptyElements>false</expandEmptyElements>
              <sortDependencies>groupId,artifactId,scope</sortDependencies>
            </sortPom>
          </pom>

          <yaml>
            <includes>
              <include>**/resources/*.yml</include>
              <include>**/resources/*.yaml</include>
            </includes>
            <jackson>
              <version>2.14.1</version>
            </jackson>
          </yaml>
        </configuration>

        <executions>
          <execution>
            <goals>
              <goal>apply</goal>
            </goals>
            <phase>compile</phase>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
