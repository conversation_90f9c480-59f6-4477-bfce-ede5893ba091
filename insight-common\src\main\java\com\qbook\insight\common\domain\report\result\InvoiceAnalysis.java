package com.qbook.insight.common.domain.report.result;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * 发票分析
 *
 * <AUTHOR>
 */
@Data
public class InvoiceAnalysis {

  @ApiModelProperty("进项发票分析")
  private InvoicePurchaseAnalysis purchaseInvoice;

  @ApiModelProperty("销项发票分析")
  private InvoiceSalesAnalysis salesInvoice;

  @ApiModelProperty("大额咨询类发票明细 (人工分析)")
  private List<Object> largeConsultingInvoices;

  @ApiModelProperty("百货、超市、药店、商场等发票列表 (人工分析)")
  private List<Object> retailSupermarketInvoices;
}
