---
logging:
  file:
    path: "/home/<USER>/insight/logs"
    name: "${spring.application.name}"
  level:
    root: "INFO"
    com.qbook: "DEBUG"
    org.springframework: "INFO"
spring:
  datasource:
    url: "**************************************************************************"
    username: "zjks"
    password: "CNzjks*^%&!"
    driver-class-name: "com.mysql.cj.jdbc.Driver"
    hikari:
      maximum-pool-size: 10
      minimum-idle: 3
      connection-timeout: 10000
      idle-timeout: 10000
  redis:
    host: "************"
    port: 6379
    password: "Zjks@123456"
    database: 10
    connect-timeout: "10s"
    timeout: "10s"
    client-name: "${spring.application.name}"
    jedis:
      pool:
        enabled: true
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: "5000ms"
apidoc:
  enable: true
  version: "1.0.0-SNAPSHOT"
  permit-all-uris:
  - "/swagger-ui.html"
  - "/swagger-resources/**"
  - "/webjars/**"
  - "/v2/api-docs"
jwt:
  header: "Authorization"
  secret: "164cfb85f5df47c9b4a5fc4c132bcdcbe8caa567ac6d435c8d798fe48e33d459"
  expiration: "180m"
  refresh-early: "20m"
wx:
  app-id: "wx46c7720d46151c49"
  app-secret: "14a4d02d3f06bdd78b8a14fdb34a89a0"
  token: "f025794b7b0f4fb0ab0170db29aa7d1c"
  aes-key: "ljKK9Bo1pSfvWQbfHGmOMaarIJfSHKLms4aAV8kGgbH"
  encryption-mode: "plain"
  qr-redis-prefix: "wx:qr:"
  qr-redis-expire: 180
  get-access-token-url: "https://api.weixin.qq.com/cgi-bin/token"
  get-ticket-url: "https://api.weixin.qq.com/cgi-bin/qrcode/create"
  get-qr-code-url: "https://mp.weixin.qq.com/cgi-bin/showqrcode"
  get-user-info-url: "https://api.weixin.qq.com/cgi-bin/user/info"
