package com.qbook.insight.handler.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qbook.insight.common.entity.IndicatorConfig;
import com.qbook.insight.common.mapper.IndicatorConfigMapper;
import com.qbook.insight.handler.service.IndicatorConfigService;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 指标计算配置接口实现
 *
 * <AUTHOR>
 */
@Service
public class IndicatorConfigServiceImpl implements IndicatorConfigService {

  @Resource private IndicatorConfigMapper indicatorConfigMapper;

  public int add(IndicatorConfig indicatorConfig) {
    return indicatorConfigMapper.insert(indicatorConfig);
  }

  public int delete(long id) {
    return indicatorConfigMapper.deleteById(id);
  }

  public int update(long id, IndicatorConfig indicatorConfig) {
    indicatorConfig.setId(id);
    return indicatorConfigMapper.updateById(indicatorConfig);
  }

  public List<IndicatorConfig> list(Integer status, String indicatorName, String indicatorCode) {
    LambdaQueryWrapper<IndicatorConfig> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(status != null, IndicatorConfig::getStatus, status);
    wrapper.eq(indicatorCode != null, IndicatorConfig::getIndicatorCode, indicatorCode);
    wrapper.eq(indicatorName != null, IndicatorConfig::getIndicatorName, indicatorName);
    wrapper.orderByDesc(IndicatorConfig::getCreatedAt);
    return indicatorConfigMapper.selectList(wrapper);
  }
}
